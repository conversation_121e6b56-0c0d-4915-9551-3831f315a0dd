<template>
  <content-panel class="panel-tabs">
    <el-row class="tabs-row">
      <el-col :span="18">
        <el-tabs v-model="activeTab" class="tabs">
         <el-tab-pane v-for="tab in getTabPermission" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="6" class="table-btn-area" v-show="activeTab&& activeTab !== 'defaultNmae'">
          <el-checkbox
            v-model="isAll"
            type="primary"
            label="全选"
            @change="changeIsall"
            border
            v-show="activeTab === 'pieceworkWageDivision'"
            size="mini"
            style="margin-right: 10px" 
            class="select"
          ></el-checkbox>
           
        <el-button type="primary" size="small" v-show="activeTab === 'PieceworkWage'"
          @click="updateView">刷新</el-button>
           <el-button type="primary" size="small" v-show="activeTab === 'pieceworkWageDivision'"
          @click="addDivision">新增</el-button>
        <el-button type="primary" size="small"  v-show="activeTab === 'pieceworkWageDivision'" @click="handleImport">导入</el-button>
        <el-button type="primary" size="small" @click="handleExport">导出</el-button>
        <el-button size="small" v-show="activeTab === 'pieceworkWageDivision'" type="primary" @click="batchDelete">
            批量删除
          </el-button>
      </el-col>
    </el-row>
    <keep-alive>
       <component :isAll="isAll" @chanIsAll="chanIsAll" :is="activeTab" ref="PlateTypePieceworkWageRef"></component>
     </keep-alive>
    <updatePieceworkWageDialog ref="updatePieceworkWageDialogRef" v-if="updateVisible" @confirm="onUpdateConfirm"
      @cancel="onUpdateCancel" :factoryList="tabList" :visible="updateVisible">
    </updatePieceworkWageDialog>
 
        <!-- 导入弹窗 -->
    <Import v-if="ImportVisible" :visible="ImportVisible" @cancel="cancel" @confirm="confirm"
      :importInfo="importInfo" />
  </content-panel>
</template>

<script>
import {getPermitList} from '@/store/modules/permission'
import GroupDetail from './components/groupDetail.vue';
import PersonalDetails from './components/personalDetails.vue';
import PieceworkWage from './components/pieceworkWage.vue';
import updatePieceworkWageDialog from "./components/updatePieceworkWageDialog.vue";
import pieceworkWageSummary from "./components/pieceworkWageSummary.vue"; 
import defaultNmae from "./components/defaultNmae.vue"; 
import pieceworkWageDivision from "./components/pieceworkWageDivision.vue"; 
const tabPermissions=[
  {
    name: 'PieceworkWage',
    label: '计件工资',
    permission: 'was-customized$pieceworkWage$plateType$PlateTypePieceworkWageDetails$pieceworkWage'
  },
  {
    name: 'PersonalDetails',
    label: '个人明细',
    permission:  'was-customized$pieceworkWage$plateType$PlateTypePieceworkWageDetails$personalDetails'
  },
  {
    name: 'GroupDetail',
    label: '集体明细', 
    permission: 'was-customized$pieceworkWage$plateType$PlateTypePieceworkWageDetails$groupDetail'
  },
   {
    name: 'pieceworkWageSummary',
     label: '集体计件汇总', 
    permission: 'was-customized$pieceworkWage$plateType$PlateTypePieceworkWageDetails$pieceworkWageSummary'
  },
  {
    name: 'pieceworkWageDivision',
    label: '集体计件工资划分', 
    permission: 'was-customized$pieceworkWage$plateType$PlateTypePieceworkWageDetails$pieceworkWageDivision'
  }

] 
export default {
  name: 'PlateTypePieceworkWageDetails',
  components: {
    GroupDetail,
    PersonalDetails,
    PieceworkWage,
    updatePieceworkWageDialog,
    pieceworkWageSummary,
    defaultNmae,
    pieceworkWageDivision
  },
  data() {
    return {
      activeTab: 'defaultNmae',
      importInfo:{},
      isAll: false,
      updateVisible: false, 
      ImportVisible:false,
      tabList: []
    };
  },
  created() {
    this.$api.plateTypeSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList = res.data.map((item) => ({
        label: item.name,
        name: item.name,
        id: item.id,
      }));
    });
  }, 
  watch: { 
     getTabPermission: {
        handler(newVal) { 
          if (newVal.length > 0 && this.activeTab=='defaultNmae') {
          this.activeTab = newVal[0].name;
        } 
      },
      deep: true,
      immediate: true,
    },
},
  computed: {
    // 获取当前tab的权限
    getTabPermission() {
      const permitList = getPermitList();  
      return tabPermissions.filter(tab => permitList.includes(tab.permission));
    },
  },
  methods: {
    //导出
    handleExport() {
      if(this.activeTab === 'pieceworkWageDivision'){
        this.$refs.PlateTypePieceworkWageRef.export();
        return;
      }
      let exportForm = JSON.parse(JSON.stringify(this.$refs.PlateTypePieceworkWageRef.filterParam));
      this.$api.common
        .doExport(this.activeTab == 'PieceworkWage' ? 'plankPieceWageSystemExport' : this.activeTab == 'GroupDetail' ? 'plankPieceWageGroup' : this.activeTab == 'PersonalDetails' ? 'plankPieceWagePerson' : 'plankPieceWageSystemTotalExport', { ...exportForm })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    //刷新
    updateView() {
      this.updateVisible = true;
    },
    onUpdateCancel() {
      this.updateVisible = false;
    },
    //新增
    addDivision(){
      this.$refs.PlateTypePieceworkWageRef.handleAddd()
    },
    //导入
    handleImport(){
      this.importInfo = {
        reportName: "plankPieceGroupAllotImport",
        paramMap: {
          columnValue: "板木-集体计件分配"
        },
      };
      this.title = "导入";
      this.ImportVisible = true;
    },
     // 导入取消回调
    cancel(value) {
      this.ImportVisible = value;
    },
  // 刷新列表
    refreshList() { 
      if (this.$refs.PlateTypePieceworkWageRef && this.$refs.PlateTypePieceworkWageRef.getList) { 
        this.$refs.PlateTypePieceworkWageRef.getList();
      }
    },
    batchDelete(){
      if (this.$refs.PlateTypePieceworkWageRef && this.$refs.PlateTypePieceworkWageRef.batchDelete) { 
        this.$refs.PlateTypePieceworkWageRef.batchDelete()
        }
    },
    changeIsall(val){
     
      if (this.$refs.PlateTypePieceworkWageRef && this.$refs.PlateTypePieceworkWageRef.changeIsall) { 
     
         this.$refs.PlateTypePieceworkWageRef.changeIsall(val)
        }
    },
    chanIsAll(value){
       this.isAll=value
    },
    // 导入确认回调
    confirm(value) {
      this.ImportVisible = value;
      this.refreshList();
    },
    onUpdateConfirm(data) {
      this.$api.plateTypePieceWageSystem
        .updateMesPieceWage({
          ...data
        }).then((res) => {
          this.updateVisible = false;
          this.$refs.PlateTypePieceworkWageRef.onSearch();
          this.$message.success("刷新成功");
        }).finally(() => {
          if (this.updateVisible) {
            this.$refs.updatePieceworkWageDialogRef.isLoading = false;
          }
        });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}
.select{
  background:#0bb78e
  color:#fff
}
>>>.el-checkbox.is-bordered.el-checkbox--mini{
    height:25px !important
}
>>>.el-checkbox__input.is-checked+.el-checkbox__label{
    color:white !important
}
>>>.el-checkbox__input.is-checked .el-checkbox__inner{
   border-color:white !important
}
.panel-tabs
    position: relative;
.panel-tabs
  >>> .main-area
    padding-top 0
.tabs-row
    display:flex;
    align-items:center;
.tabs-row:after
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #E4E7ED;
    z-index: 1;
.table-btn-area
    display:flex;
    justify-content:flex-end;
    margin-right:12px;
.tabs
  >>> .el-tabs__header
    margin-bottom 5px
    .el-tabs__nav-wrap::after{
      display:none;
    }
</style>
