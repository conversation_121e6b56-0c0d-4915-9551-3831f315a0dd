import permission from './permission';
import decimalLimit from './decimalLimit';

const install = function (Vue) {
  Vue.directive('permission', permission);
  Vue.directive('decimalLimit', decimalLimit);
};

if (window.Vue) {
  window['permission'] = permission;
  window['decimalLimit'] = decimalLimit;
  Vue.use(install); // eslint-disable-line
}

permission.install = install;
export default permission;
