import permission from './permission';
import decimalLimit from './decimalLimit';

const install = function (Vue) {
  Vue.directive('permission', permission);
  Vue.directive('decimalLimit', decimalLimit);
};

if (window.Vue) {
  window['permission'] = permission;
  window['decimalLimit'] = decimalLimit;
  Vue.use(install); // eslint-disable-line
}

// 创建一个包含所有指令的插件对象
const directivesPlugin = {
  install,
  permission,
  decimalLimit
};

// 为了向后兼容，保留原有的导出方式
permission.install = install;

// 导出插件对象作为默认导出
export default directivesPlugin;

// 也可以单独导出各个指令
export { permission, decimalLimit };
