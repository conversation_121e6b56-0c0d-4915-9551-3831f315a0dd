/**
 * 小数位数限制指令
 * 用法: v-decimal-limit="2" (限制2位小数)
 * 默认限制2位小数
 */
export default {
  bind(el, binding) {
    const input = el.querySelector('input') || el;
    const decimalPlaces = binding.value || 2;
    
    // 存储原始的input事件处理器
    const originalInputHandler = input.oninput;
    
    input.addEventListener('input', function(e) {
      let value = e.target.value;
      const dotIndex = value.indexOf('.');
      
      if (dotIndex > -1) {
        // 限制小数位数
        const limitedValue = value.slice(0, dotIndex + decimalPlaces + 1);
        if (value !== limitedValue) {
          e.target.value = limitedValue;
          
          // 触发Vue的input事件以更新v-model
          const inputEvent = new Event('input', { bubbles: true });
          e.target.dispatchEvent(inputEvent);
        }
      }
      
      // 如果有原始的input处理器，也要执行
      if (originalInputHandler) {
        originalInputHandler.call(this, e);
      }
    });
  },
  
  update(el, binding) {
    // 如果绑定值发生变化，重新绑定
    if (binding.value !== binding.oldValue) {
      this.unbind(el);
      this.bind(el, binding);
    }
  },
  
  unbind(el) {
    const input = el.querySelector('input') || el;
    // 移除事件监听器
    input.removeEventListener('input', this._inputHandler);
  }
};
