import axios from "@/utils/axios";
import { downloadFile } from "@/utils"; 

export const APP_CODE = "was";
export const SUB_APP_CODE = "was-customized";

// 上传
function upload(url, fileData, params = {}, otherData = {}) {
  const formData = new FormData();

  if (Array.isArray(fileData)) {
    fileData.forEach((item) => formData.append("file", item, item.name));
  } else {
    formData.set("file", fileData, fileData.name);
  }

  for (const [key, value] of Object.entries(otherData)) {
    if (key) {
      formData.append(key, value);
    }
  }

  return axios.post(url, formData, {
    params,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
// 按照模块，export对象，避免命名冲突；也可直接export方法，优先选择export对象
// 定义方法时，描述参数结构，传递参数后进行结构，而不要直接传递data或者params对象
// 示例:
export const demo = {
  uploadFile(file) {
    return upload("/xxxx", file);
  },
  downloadFile(fileId) {
    return axios
      .get(`/design-file-share/file/download/${fileId}`, {
        responseType: "blob",
      })
      .then((response) => {
        downloadFile(response);
      });
  },
};
export const user = {};
export const common = {
  login({ userName, password }) {
    return axios.post("/qu-platform-auth-api/auth/tokenWithAD", {
      userName,
      password,
      appCode: SUB_APP_CODE,
    });
  },
  importFile(file, taskDTO) {
    return upload(
      "/qu-platform-excel-api/excel/import/task",
      file,
      {},
      {
        taskDTO: JSON.stringify(taskDTO),
      }
    );
  },
  getMenus() {
    return axios.post(
      "/qu-platform-auth-api/auth/acl/tree/user",
      {},
      {
        params: {
          appCode: APP_CODE,
          subAppCode: SUB_APP_CODE,
        },
      }
    );
  },
  checkPermission(account) {
    return axios.post("/qu-platform-auth-api/user/release/auth", {
      account,
      appCode: SUB_APP_CODE,
    });
  },
  doExport(reportName, paramMap) {
    return axios.post("/qu-platform-excel-api/excel/export/task", {
      appCode: SUB_APP_CODE,
      reportName,
      paramMap,
    });
  },
  //动态列导出
  dynamicColumnExport(data) {
    return axios.post(
      "/qu-platform-excel-api/excel/customColumn/export/task",
      data
    );
  },
  // 获取按钮的权限列表
  getButtonPermission() {
    return axios.post(
      "/qu-platform-auth-api/auth/acl/list/user/button",
      {},
      {
        params: {
          subAppCode: SUB_APP_CODE,
        },
      }
    );
  },
  getUserInfo() {
    return axios.get("/qu-platform-auth-api/user/home/<USER>/info");
  },
  // 我的应用
  getMyApps() {
    return axios.get("/qu-platform-auth-api/myApp/list");
  },
  //文件上传
  appUploadFile(fileData) {
    const formData = new FormData();
    if (Array.isArray(fileData)) {
      fileData.forEach((item) => formData.append("file", item, item.name));
    } else {
      formData.set("file", fileData, fileData.name);
    }
    formData.append("appCode", SUB_APP_CODE);
    return axios.post("/qu-file-upload-api/upload/app/uploadFile", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
  //删除正式文件
  deleteUploadFile(data) {
    return axios.post("/qu-file-upload-api/upload/file/delete", data);
  },
};
//工作台-工作总览
export const workbench = {
  //获取任务总览
  getTaskOverviewList(data) {
    return axios.post("/was-customized/salary/work/getTaskSummary", data);
  },
  //待办任务详情
  taskDetail(data) {
    return axios({
      url: "/was-customized/salary/work/taskDetail",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //获取代办任务列表
  getAgencyTasks(data) {
    return axios.post("/was-customized/salary/backlog/list", data);
  },
  //提交
  submit(data) {
    return axios.post("/was-customized/salary/backlog/commit", data);
  },
  //退回
  back(data) {
    return axios.post("/was-customized/salary/backlog/sendBack", data);
  },
  //获取个税扣款列表
  getIndividualTaxList(data) {
    return axios.post("/was-customized/salary/individualIncomeTax/list", data);
  },
  //个税扣款统计
  IndividualTaxStatistics(data) {
    return axios.post(
      "/was-customized/salary/individualIncomeTax/statistic",
      data
    );
  },
  //编辑个税扣款
  editIndividualTax(data) {
    return axios.post("/was-customized/salary/individualIncomeTax/edit", data);
  },
  //获取所属工厂列表
  getBelongFactories(data) {
    return axios.post(
      "/was-customized/salary/individualIncomeTax/belongFactories",
      data
    );
  },
  //新增个税扣款
  addIndividualTax(data) {
    return axios.post("/was-customized/salary/individualIncomeTax/add", data);
  },
  //删除个税扣款
  deleteIndividualTax(data) {
    return axios.post(
      "/was-customized/salary/individualIncomeTax/delete",
      data
    );
  },
  //个税提交
  IndividualSubmit(data) {
    return axios({
      url: "/was-customized/salary/individualIncomeTax/commit",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //个税扣款详情
  viewEdit(data) {
    return axios({
      url: "/was-customized/salary/individualIncomeTax/viewEdit",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //获取工资表
  getPayroll(data) {
    return axios.post("/was-customized/salary/salary/total", data);
  },
  //新增员工工资
  addSalary(data) {
    return axios.post("/was-customized/salary/salary/addSalary", data);
  },
  //编辑工资表工序
  editSalaryProcedure(data) {
    return axios.post("/was-customized/salary/salary/editProcess", data);
  },
  //删除工资表
  deleteSalary(params) {
    return axios({
      url: "/was-customized/salary/salary/delete",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //工资表--查看明细
  lookSalary(data) {
    return axios.post("/was-customized/salary/salary/detail", data);
  },
  //获取分配表
  getAllocation(data) {
    return axios.post(
      "/was-customized/salary/work/getAllotByFactoryMonth",
      data
    );
  },
  //编辑分配表
  editAllocation(data) {
    return axios.post("/was-customized/salary/allot/edit", data);
  },
  //分配表导出
  allotExport(data) {
    return axios.post("/was-customized/salary/allot/export", data);
  },
  //获取上卡现金表
  getCash(data) {
    return axios.post(
      "/was-customized/salary/work/getBankCashByFactoryMonth",
      data
    );
  },
  //分厂调整分页获取
  getEditSalaryList(data) {
    return axios.post(
      "/was-customized/salary/factoryEditSalary/getEditList",
      data
    );
  },
  //查询工厂下的产线
  getproductLineList(data) {
    return axios({
      url: "/was-customized/productLine/list",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //根据工厂查询是否展示产线列表接口
  getlistProductLineTab(data) {
    return axios({
      url: "/was-customized/salary/factoryEditSalary/listProductLineTab",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整统计
  getStatistic(data) {
    return axios.post(
      "/was-customized/salary/factoryEditSalary/statistics",
      data
    );
  },
  //分厂调整提交
  factoryEditSalarySubmit(data) {
    return axios({
      url: "/was-customized/salary/factoryEditSalary/submit",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整余留管理
  factoryEditSalaryAdjust(data) {
    return axios.post("/was-customized/salary/factoryEditSalary/adjust", data);
  },
  //分厂调整明细
  factoryEditSalaryDetail(data) {
    return axios({
      url: "/was-customized/salary/factoryEditSalary/editDetail",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整新增
  addSaveFactoryEdit(data) {
    return axios.post(
      "/was-customized/salary/factoryEditSalary/addSaveFactoryEdit",
      data
    );
  },
  //分厂调整编辑
  updateSaveFactoryEdit(data) {
    return axios.post(
      "/was-customized/salary/factoryEditSalary/updateSaveFactoryEdit",
      data
    );
  },
  //分厂调整删除
  deleteFactoryEdit(data) {
    return axios({
      url: "/was-customized/salary/factoryEditSalary/deleteFactoryEdit",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整详情
  viewFactoryEdit(data) {
    return axios({
      url: "/was-customized/salary/factoryEditSalary/viewFactoryEdit",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //分厂调整审核
  adjustmentAudit(data) {
    return axios.post("/was-customized/salary/work/adjustmentAudit", data);
  },
  //分厂调整记录表
  adjustmentRecord(data) {
    return axios({
      url: "/was-customized/salary/factoryEditSalary/editRecord",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //调整前工资表列表
  beforeSalary(data) {
    return axios.post("/was-customized/salary/before/list", data);
  },
  //编辑上卡现金信息
  editCach(data) {
    return axios.post("/was-customized/salary/bankCash/edit", data);
  },
  //结束收集生成工资表
  finishCollecting(data) {
    return axios.post("/was-customized/salary/work/finishCollecting", data);
  },
  //提交一审
  submitFirstAudit(params) {
    return axios({
      url: "/was-customized/salary/work/submitFirstAudit",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //一审通过或驳回
  firstAudit(data) {
    return axios.post("/was-customized/salary/work/firstAudit", data);
  },
  //提交二审
  submitSecondAudit(params) {
    return axios({
      url: "/was-customized/salary/work/submitSecondAudit",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //二审通过或驳回
  secondAudit(data) {
    return axios.post("/was-customized/salary/work/secondAudit", data);
  },
  //强制退回
  forceBack(data) {
    return axios({
      url: "/was-customized/salary/work/forceBack",
      method: "post",
      data,
    });
  },
  //查询工资明细的表头
  listAllColumns(params) {
    return axios({
      url: "/was-customized/system/scene/listAllColumns",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //自定义场景配置新增
  sceneConfigurationAdd(data) {
    return axios.post("/was-customized/system/scene/save", data);
  },
  //自定义场景配置编辑
  sceneConfigurationEdit(data) {
    return axios.post("/was-customized/system/scene/update", data);
  },
  //自定义场景列表
  sceneConfigurationList(moduleType) {
    return axios({
      url: "/was-customized/system/scene/listAllScene",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: {
        moduleType,
      },
    });
  },
  //自定义场景配置删除
  sceneConfigurationDelete(params) {
    return axios({
      url: "/was-customized/system/scene/delete",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //自定义场景添加配置列
  addColumns(data) {
    return axios.post("/was-customized/system/scene/addColumns", data);
  },
  //特殊工资单状态统计
  specialStatistics(params) {
    return axios({
      url: "/was-customized/salary/salary/specialStatistics",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  //是否具有总览任务操作权限
  overviewPermission(data) {
    return axios.post(
      "/was-customized/salary/work/hasSummaryTaskPermission",
      data
    );
  },
  //是否具有待办任务操作权限
  agencyPermission(data) {
    return axios.post(
      "/was-customized/salary/work/hasTodoTaskPermission",
      data
    );
  },
  //工资表明细同步更新
  salaryUpdate(data) {
    return axios.post("/was-customized/salary/salary/syncRefresh", data);
  },
  //查询考勤中的班组信息
  queryGroup(data) {
    return axios.post("/was-customized/salary/salary/queryGroup", data);
  },
  //批量备注二
  batchRemark(data) {
    return axios.post("/was-customized/salary/salary/batchRemark", data);
  },
  //财务标记提交
  financialMark(data) {
    return axios({
      url: "/was-customized/salary/work/financialMark",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //汇总表
  summarySheet(data) {
    return axios.post("/was-customized/salary/salary/summarySheet", data);
  },
  //汇总财务表
  financialSheet(data) {
    return axios.post("/was-customized/salary/salary/financialSheet", data);
  },
  //编辑汇总财务表
  editSummaryFinance(data) {
    return axios.post(
      "/was-customized/salary/salary/editSummaryFinanceSheetDetail",
      data
    );
  },
  //根据工厂和核算月份查询班组列表
  availableGroups(data) {
    return axios({
      url: "/was-customized/salary/salary/availableGroups",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //可用工厂列表
  availableFactories() {
    return axios.post("/was-customized/salary/work/availableFactories");
  },
  //导出
  exportComon(data) {
    return axios.post("/was-customized/salary/salary/summary/export", data);
  },
  //工资表打印表头
  getPrintHeader(data) {
    return axios.post("/was-customized/salary/salary/getPrintHeader", data);
  },
  //工资表打印导出
  exportSalaryPrint(data) {
    return axios.post("/was-customized/salary/salary/exportSalaryPrint", data, {
      responseType: "bolb",
    });
  },
  //分厂调整导出
  factoryEditSalaryExport(data) {
    return axios.post("/was-customized/salary/factoryEditSalary/export", data);
  },
  //获取可操作核算月份
  availableMonths() {
    return axios.post("/was-customized/salary/work/availableMonths");
  },
  //是否展示分配表及上卡现金表标签栏
  showTabs(data) {
    return axios({
      url: "/was-customized/salary/work/showTabs",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //结束收集校验
  checkFinishCollecting(data) {
    return axios({
      url: "/was-customized/salary/work/checkFinishCollecting",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //提交一审校验
  checkSubmitFirstAudit(data) {
    return axios({
      url: "/was-customized/salary/work/checkSubmitFirstAudit",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //提交二审校验
  checkSubmitSecondAudit(data) {
    return axios({
      url: "/was-customized/salary/work/checkSubmitSecondAudit",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //动态列头列表
  columnHeads(data) {
    return axios.post("/was-customized/salary/salary/columnHeads", data);
  },
  //汇总表明细
  viewDetail(data) {
    return axios.post(
      "/was-customized/salary/ManualAccounting/viewDetail",
      data
    );
  },
  //汇总表编辑
  editManualAccounting(data) {
    return axios.post("/was-customized/salary/ManualAccounting/edit", data);
  },
};
//信息台账
export const information = {
  //员工管理
  employee: {
    //获取员工列表
    employeeList({ pageSize, pageNum, filterData }) {
      return axios.post("/was-customized/salary/staff/list", {
        pageSize,
        pageNum,
        filterData,
      });
    },
    //调职记录
    searchTransferRecord(data) {
      return axios.get("/was-customized/salary/staff/transfer/record?staffCode="+data.staffCode);
    },
    //新增或修改员工
    addEmployee(data) {
      return axios.post("/was-customized/salary/staff/saveOrUpdate", data);
    },
    //删除员工
    deleteEmployee(data) {
      return axios.post("/was-customized/salary/staff/delete", data);
    },
    //查询历史工号详情
    employeeDetails(data) {
      return axios.post("/was-customized/salary/staff/detail", data);
    },
    //员工列表统计
    employeeStatistics(data) {
      return axios.post("/was-customized/salary/staff/statistic", data);
    },
    //身份证解码
    decrypt(str) {
      return axios.get("/security/private/full/decrypt", {
        params: { str },
      });
    },
    //银行卡解码
    idCardDecrypt(data) {
      return axios.post("/was-customized/salary/staff/decode", data);
    },
    //试用员工身份证解码
    idCardDecodeStaff(data) {
      return axios({
        url: "/was-customized/salary/staff/idCardDecode",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //历史工号
    historyStaff(data) {
      return axios({
        url: "/was-customized/salary/staff/history",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //解冻
    unfreezeStaff(data) {
      return axios({
        url: "/was-customized/salary/staff/unfreezeStaff",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //员工详情
    staffDetail(data) {
      return axios.post("/was-customized/salary/staff/staffDetail", data);
    },
  },
  //余留管理
  remaining: {
    //获取工厂余留列表
    getRemainingList(data) {
      return axios.post("/was-customized/info/factoryRemain/getRemain", data);
    },
    //导出工厂余留列表
    exportRemainingList(data) {
      return axios.post("/was-customized/info/factoryRemain/export", data);
    },
    //添加余留
    addRemaining(data) {
      return axios.post(
        "/was-customized/info/factoryRemain/addNewRemain",
        data
      );
    },
    //获取工厂详细余留
    getDetailedRemaining(data) {
      return axios.post(
        "/was-customized/info/factoryRemain/getRemainDetailByFactory",
        data
      );
    },
    //根据月份获取余留
    getRemainByMonth(data) {
      return axios.post(
        "/was-customized/info/factoryRemain/getRemainByMonth",
        data
      );
    },
  },
  //借支台账
  debitAccount: {
    //借支台账列表
    getDebitAccountList({ pageSize, pageNum, filterData }) {
      return axios.post("/was-customized/salary/borrowing/list", {
        pageSize,
        pageNum,
        filterData,
      });
    },
    // 借支清单列表
    getDebitDetailAccountList({ pageSize, pageNum, filterData }) {
      return axios.post("/was-customized/salary/borrowing/detailList", {
        pageSize,
        pageNum,
        filterData,
      });
    },
    // 借支清单统计
    debitListStatistics(data) {
      return axios.post(
        "/was-customized/salary/borrowing/listStatistics",
        data
      );
    },
    //确认提交
    confirmSubmission(data) {
      return axios.post("/was-customized/salary/borrowing/confirmCommit", data);
    },
    //借支台账取消
    confirmCancel(data) {
      return axios.post("/was-customized/salary/borrowing/cancel", data);
    },
    //无需分期
    noAccounting(data) {
      return axios.post("/was-customized/salary/borrowing/noAccounting", data);
    },

    //分期还款
    amortizationLoan(data) {
      return axios.post(
        "/was-customized/salary/borrowing/generatePeriods",
        data
      );
    },
    //借支台账详情
    debitAccountDetails(data) {
      return axios.post(
        "/was-customized/salary/borrowing/borrowingDetail",
        data
      );
    },
    //借支台账列表统计
    debitLedgerListStatistics(data) {
      return axios.post("/was-customized/salary/borrowing/statistic", data);
    },
    //批量确认借支台账
    batchConfirmDebitLedger(data) {
      return axios.post("/was-customized/salary/borrowing/batchProcess", data);
    },
    //退回
    back(data) {
      return axios.post("/was-customized/salary/borrowing/sendBack", data);
    },
    //回退无需分期
    rollBackNoAccounting(data) {
      return axios.post(
        "/was-customized/salary/borrowing/rollBackNoAccounting",
        data
      );
    },

    //提交
    submit(data) {
      return axios.post("/was-customized/salary/borrowing/commit", data);
    },
    //获取核算月份
    getAccountingMonth(data) {
      return axios({
        url: "/was-customized/salary/borrowing/availableMonth",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //修改实付金额
    updateActualAmount(data) {
      return axios({
        url: "/was-customized/salary/borrowing/updateActualAmount",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量选择总数统计
    batchConfirmCount(data) {
      return axios.post(
        "/was-customized/salary/borrowing/batchConfirmCount",
        data
      );
    },
    //变动数据
    obsDebitChangeData(data) {
      return axios.post("/was-customized/obsDebit/changeData", data);
    },
    //处理变动数据
    handleChangeData(data) {
      return axios({
        url: "/was-customized/obsDebit/handle",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },

  //奖惩台账
  punishment: {
    //获取奖惩数据
    getPunishmentList(data) {
      return axios.post("/was-customized/info/RewardPunishment/getList", data);
    },
    //新增奖惩
    addPunishment(data) {
      return axios.post(
        "/was-customized/info/RewardPunishment/saveOrUpdate",
        data
      );
    },
    // 更新状态
    uploadStatus(data) {
      return axios.post(
        "/was-customized/info/RewardPunishment/updateStatus",
        data
      );
    },
    //统计
    rewardsStatistics(data) {
      return axios.post(
        "/was-customized/info/RewardPunishment/statistic",
        data
      );
    },
    //删除奖惩
    deletePunishment(data) {
      return axios({
        url: "/was-customized/info/RewardPunishment/delete",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },

    //批量处理变动数据
    multipleHandle(data) {
      return axios.post("/was-customized/hr/rewardPunish/multipleHandle", data);
    },
  },
  //特殊工资单管理
  payrollManagement: {
    //特殊工资单列表
    specialSalaryList(data) {
      return axios.post("/was-customized/specialSalary/list", data);
    },
    //特殊工资单统计
    statistics(data) {
      return axios.post("/was-customized/specialSalary/statistics", data);
    },
    //新增特殊工资单
    addSpecialSalary(data) {
      return axios.post("/was-customized/specialSalary/add", data);
    },
    //编辑特殊工资单
    editSpecialSalary(data) {
      return axios.post("/was-customized/specialSalary/update", data);
    },
    //编辑特殊工资单详情
    editSalaryDetail() {
      return axios({
        url: "/was-customized/specialSalary/editSalaryDetail",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    //特殊工资单详情
    salaryDetail(data) {
      return axios({
        url: "/was-customized/specialSalary/detail",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //删除特殊工资单详情
    deleteSalaryDetail(data) {
      return axios.post(
        "/was-customized/specialSalary/deleteSalaryDetail",
        data
      );
    },
    //根据月份查询工资单详情
    monthSalary(data) {
      return axios.post("/was-customized/specialSalary/monthSalary", data);
    },
    //工资单详情修改记录
    salaryDetailLog() {
      return axios({
        url: "/was-customized/specialSalary/salaryDetailLog",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    //批量提交
    batchSubmit(data) {
      return axios.post("/was-customized/specialSalary/batchSubmit", data);
    },
    //扣款明细
    listDeductDetail(data) {
      return axios.post("/was-customized/specialSalary/listDeductDetail", data);
    },
    //提交
    submit(data) {
      return axios({
        url: "/was-customized/specialSalary/submit",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量审核
    batchAudit(data) {
      return axios.post("/was-customized/specialSalary/batchAudit", data);
    },
    //退回
    sendBack(data) {
      return axios({
        url: "/was-customized/specialSalary/sendBack",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //退回原因
    sendBackReason(data) {
      return axios({
        url: "/was-customized/specialSalary/sendBackReason",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //作废
    revoke(data) {
      return axios({
        url: "/was-customized/specialSalary/revoke",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //通过
    audit(data) {
      return axios({
        url: "/was-customized/specialSalary/audit",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //打印次数
    increment(data) {
      return axios.post("/was-customized/specialSalary/increment", data);
    },
    //特殊工资单明细列表
    specialSalaryDetail(data) {
      return axios.post("/was-customized/specialSalary/detailList", data);
    },
  },
  //成本赔偿扣款台账

  costCompensation: {
    //无需核算
    noAccounting(data) {
      return axios.post("/was-customized/bpmDeduct/noAccounting", data);
    },
    //成本扣款台账列表
    deductList(data) {
      return axios.post("/was-customized/bpmDeduct/list", data);
    },
    //统计
    deductStatistics(data) {
      return axios.post("/was-customized/bpmDeduct/statistic", data);
    },
    //成本扣款台账新增
    saveDeduct(data) {
      return axios.post("/was-customized/bpmDeduct/save", data);
    },
    //成本扣款台账编辑
    updateDeduct(data) {
      return axios.post("/was-customized/bpmDeduct/update", data);
    },
    updateActualAmount(data) {
      return axios({
        url: "/was-customized/bpmDeduct/updateActualAmount",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //成本扣款台账核算
    businessAccounting(data) {
      return axios.post("/was-customized/bpmDeduct/businessAccounting", data);
    },
    //实际扣款厂牌下拉
    getAllStaffCode(data) {
      return axios({
        url: "/was-customized/salary/staff/getAllStaffCode",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },

    //成本扣款台账回退
    backBpmDeduct(data) {
      return axios.post("/was-customized/bpmDeduct/backBpmDeduct", data);
    },
    //回退无需分期
    rollBackNoAccounting(data) {
      return axios.post("/was-customized/bpmDeduct/rollBackNoAccounting", data);
    },
    //成本扣款台账删除
    deleteBpmDeduct(data) {
      return axios.post("/was-customized/bpmDeduct/deleteBpmDeduct", data);
    },
    //成本扣款台账批量核算
    batchBusinessAccounting(data) {
      return axios.post(
        "/was-customized/bpmDeduct/batchBusinessAccounting",
        data
      );
    },
    //成本扣款台账查看详情
    detailDeduct(data) {
      return axios.post("/was-customized/bpmDeduct/viewDetail", data);
    },
    //成本扣款台账批量回退
    batchBackBpmDeduct(data) {
      return axios.post("/was-customized/bpmDeduct/batchBackBpmDeduct", data);
    },
    //成本扣款台账批量删除
    batchDeleteBpmDeduct(data) {
      return axios.post("/was-customized/bpmDeduct/batchDeleteBpmDeduct", data);
    },
    //变动数据
    changeData(data) {
      return axios.post("/was-customized/bpm/cost/changeData", data);
    },
    //处理变动数据
    handleCost(data) {
      return axios({
        url: "/was-customized/bpm/cost/handle",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //分期
    amortize(data) {
      return axios.post("/was-customized/bpmDeduct/amortize", data);
    },
  },
  //未付款台账
  unpaidLedger: {
    //未付款台账列表
    unPaidList(data) {
      return axios.post("/was-customized/unPaid/list", data);
    },
    //编辑未付款台账
    editUnPaidList(data) {
      return axios.post("/was-customized/unPaid/edit", data);
    },
    //删除未付款台账
    deleteUnPaid(data) {
      return axios({
        url: "/was-customized/unPaid/delete",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //申请付款
    unPaidSupplyPay(data) {
      return axios.post("/was-customized/unPaid/supplyPay", data);
    },
    //取消申请
    unPaidCancelSupply(data) {
      return axios.post("/was-customized/unPaid/cancelSupply", data);
    },
    //付款确认
    unPaidPayConfirm(data) {
      return axios.post("/was-customized/unPaid/payConfirm", data);
    },
    //扣款备注
    unPaidPayRemark(data) {
      return axios.post("/was-customized/unPaid/remark", data);
    },
    //未付款台账详情
    unPaidDetail(data) {
      return axios({
        url: "/was-customized/unPaid/detail",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  //工资查询
  salarySearch: {
    //工资查询列表
    salaryQuery(data) {
      return axios.post("/was-customized/salary/salary/salaryQuery", data);
    },
    //表头
    salaryQueryColumns() {
      return axios.get("/was-customized/salary/salary/salaryQueryColumns");
    },
    //导出
    exportSalaryQuery(data, isFilterEmpty) {
      return axios.post(
        "/was-customized/salary/salary/exportSalaryQuery?isFilterEmpty=" + isFilterEmpty,
        data
      );
    },
  },
  //奖惩台账
  rewardLedger: {
    noAccounting(data) {
      return axios.post(
        "/was-customized/RewardPunishmentLedger/noAccounting",
        data
      );
    },
    //奖惩台账列表
    rewardList(data) {
      return axios.post("/was-customized/RewardPunishmentLedger/getList", data);
    },
    //HR变动数据
    hrChangeData(data) {
      return axios.post("/was-customized/hr/rewardPunish/hrChangeData", data);
    },
    //统计
    rewardStatistic(data) {
      return axios.post(
        "/was-customized/RewardPunishmentLedger/statistic",
        data
      );
    },
    //新增
    addReward(data) {
      return axios.post("/was-customized/RewardPunishmentLedger/save", data);
    },
    //奖惩台账明细
    viewDetail(data) {
      return axios({
        url: "/was-customized/RewardPunishmentLedger/viewDetail",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //删除
    deleteReward(data) {
      return axios({
        url: "/was-customized/RewardPunishmentLedger/delete",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //处理变动数据
    rewardPunish(data) {
      return axios({
        url: "/was-customized/hr/rewardPunish/handle",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //退回||批量退回
    rollback(data) {
      return axios.post(
        "/was-customized/RewardPunishmentLedger/rollback",
        data
      );
    },
    //回退无需分期
    rollBackNoAccounting(data) {
      return axios.post(
        "/was-customized/RewardPunishmentLedger/rollBackNoAccounting",
        data
      );
    },

    //核算
    businessAccounting(data) {
      return axios.post(
        "/was-customized/RewardPunishmentLedger/businessAccounting",
        data
      );
    },
    //奖惩-修改实际扣款金额
    rewardUpdateActualAmount(data) {
      return axios({
        url: "/was-customized/RewardPunishmentLedger/updateActualAmount",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量核算
    batchBusinessAccounting(data) {
      return axios.post(
        "/was-customized/RewardPunishmentLedger/batchBusinessAccounting",
        data
      );
    },
    //奖惩批量删除
    batchDeleteReward(data) {
      return axios.post(
        "/was-customized/RewardPunishmentLedger/batchDelete",
        data
      );
    },
  },
  //新熟手补贴名单
  newEmployeeSubsidies: {
    //新员工补贴名单-列表接口
    newstaffList(data) {
      return axios.post("/was-customized/newStaff/subsidy/list/list", data);
    },
    //新员工补贴名单-状态数量统计接口
    newstafftotal(data) {
      return axios.post("/was-customized/newStaff/subsidy/list/total", data);
    },
    //新员工补贴名单-新增接口
    newstaffsave(data) {
      return axios.post("/was-customized/newStaff/subsidy/list/save", data);
    },
    //新员工补贴名单-编辑接口
    newstaffedit(data) {
      return axios.post("/was-customized/newStaff/subsidy/list/edit", data);
    },
    //新员工补贴名单-删除接口
    newstaffdelete(data) {
      return axios.post("/was-customized/newStaff/subsidy/list/delete", data);
    },
  },
};
//数据上传
export const dataUpload = {
  //员工考勤
  employeeAttendance: {
    //获取员工考勤列表
    getEmployeeAttendanceList({ pageNum, pageSize, filterData }) {
      return axios.post("/was-customized/salary/attendance/getAttendanceList", {
        pageNum,
        pageSize,
        filterData,
      });
    },
    //新增员工考勤
    addAttendance(data) {
      return axios.post("/was-customized/salary/attendance/add", data);
    },
    // 编辑修改员工考勤
    reviseEmployee(data) {
      return axios.post("/was-customized/salary/attendance/update", data);
    },
    //岗位更新
    updateEmployee(data) {
      return axios.post("/was-customized/salary/attendance/jobUpdate", data);
    },
    //员工考勤列表统计
    employeeAttendanceStatistics(data) {
      return axios.post("/was-customized/salary/attendance/statistic", data);
    },
    //删除员工考勤
    deleteEmployee(params) {
      return axios({
        url: "/was-customized/salary/attendance/delete",
        method: "get",
        params,
      });
    },
    //根据工厂和核算月份查询班组列表
    getGroups(data) {
      return axios({
        url: "/was-customized/salary/attendance/groups",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //查询员工关联考勤
    queryStaff(data) {
      return axios.post("/was-customized/salary/attendance/queryStaff", data);
    },
  },
  //计件工资
  pieceRateWage: {
    //获取计件工资列表
    getPieceRateWageList({ pageNum, pageSize, filterData }) {
      return axios.post("/was-customized/salary/pieceWage/getPieceWageList", {
        pageNum,
        pageSize,
        filterData,
      });
    },
    //获取下标总记录
    getSubscriptList(data) {
      return axios.post("/was-customized/salary/pieceWage/statistics", data);
    },
    //获取同步更新任务状态
    syncTaskStatus(data) {
      return axios.post(
        "/was-customized/salary/pieceWage/syncTaskStatus",
        data
      );
    },
    //计件工资（系统）修改
    systemPiece(data) {
      return axios.post(
        "/was-customized/salary/pieceWage/editSystemAmount",
        data
      );
    },
    //计件工资（调整）修改
    adjustmentPiece(data) {
      return axios.post("/was-customized/salary/pieceWage/editAmount", data);
    },
    //同步更新
    syncUpdate(data) {
      return axios({
        url: "/was-customized/salary/pieceWage/syncUpdate",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    // 获取计件工资调账明细
    adjustDetail(data) {
      return axios.post("/was-customized/salary/pieceWage/adjustDetail", data);
    },
    // 计件调整
    adjustAmount(data) {
      return axios.post("/was-customized/salary/pieceWage/adjustAmount", data);
    },
    //根据工厂和核算月份查询班组列表
    getGroups(data) {
      return axios({
        url: "/was-customized/salary/attendance/groups",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  // 集体账户
  collectiveAccount: {
    //获取集体账户列表
    getCollectiveAccountList({ pageNum, pageSize, filterData }) {
      return axios.post(
        "/was-customized/salary/collectiveAccount/getCollective",
        { pageNum, pageSize, filterData }
      );
    },
    //获取下标总记录
    getCollectiveAccount(data) {
      return axios.post("/was-customized/salary/pieceWage/getAllDetail", data);
    },
    //调账
    accountAdjustment(data) {
      return axios.post(
        "/was-customized/salary/collectiveAccount/adjust",
        data
      );
    },
  },
  //其他补贴
  otherSubsidies: {
    //其他补贴分页查询
    getOtherSubsidyList(data) {
      return axios.post("/was-customized/otherSubsidy/list", data);
    },
    //其他补贴新增
    getOtherSubsidySave(data) {
      return axios.post("/was-customized/otherSubsidy/save", data);
    },
    // 其他补贴编辑
    getOtherSubsidyUpdate(data) {
      return axios.post("/was-customized/otherSubsidy/update", data);
    },
    // 其他补贴删除
    getOtherSubsidyDelete(data) {
      return axios.post("/was-customized/otherSubsidy/delete", data);
    },
    // 其他补贴提交
    getOtherSubsidySubmit({ id, status }) {
      return axios.post("/was-customized/otherSubsidy/submit", {
        id,
        status,
      });
    },
  },
  //其他扣款
  OtherDeductions: {
    //其他扣款分页查询
    getotherDeductionList(data) {
      return axios.post("/was-customized/otherDeduction/list", data);
    },
    //其他扣款新增
    getOtherrDeductionSave(data) {
      return axios.post("/was-customized/otherDeduction/save", data);
    },
    // 其他扣款编辑
    getOtherDeductionUpdate(data) {
      return axios.post("/was-customized/otherDeduction/update", data);
    },
    // 其他扣款删除
    getOtherDeductionDelete(data) {
      return axios.post("/was-customized/otherDeduction/delete", data);
    },
    // 其他扣款提交
    getOtherDeductionSubmit(data) {
      return axios.post("/was-customized/otherDeduction/submit", data);
    },
    // 其他扣款动态列头
    getOtherDeductionHead({ planid }) {
      return axios({
        url: "/was-customized/otherDeduction/head",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: planid,
      });
    },
    // 其他扣款动态列数据
    getOtherDeductionHeadData(data) {
      return axios({
        url: "/was-customized/otherDeduction/data",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //其他扣款导入
    Otherimport(data) {
      return axios.post("/was-customized/otherDeduction/import", data);
    },
  },
  // 社保扣款
  SocialSecurity: {
    // 批量开启或关闭20元社保
    batchOnOrOffInsurance(data) {
      return axios.post(
        "/was-customized/salary/socialSecurity/batchOnOrOffInsurance",
        data
      );
    },
    // 查询社保扣款列表
    getSocialSecurityList(data) {
      return axios.post("/was-customized/salary/socialSecurity/list", data);
    },
    batchDelete(data) {
      return axios.post(
        "/was-customized/salary/socialSecurity/batchDelete",
        data
      );
    },
    // 新增-修改社保扣款
    getSocialSecuritySaveOrUpdate(data) {
      return axios.post("/was-customized/salary/socialSecurity/add", data);
    },
    // 修改社保扣款
    getSocialSecuritySaveOrEdit(data) {
      return axios.post("/was-customized/salary/socialSecurity/edit", data);
    },
    // 删除社保扣款
    getSocialSecurityDelete(data) {
      return axios.post("/was-customized/salary/socialSecurity/delete", data);
    },
    // 社保扣款列表页面统计
    getSocialSecurityStatistic(data) {
      return axios.post(
        "/was-customized/salary/socialSecurity/statistic",
        data
      );
    },
    //20元社保扣款明细
    twenties(data) {
      return axios.post("/was-customized/salary/socialSecurity/twenties", data);
    },
    //社保扣款查看明细
    viewEdit(data) {
      return axios({
        url: "/was-customized/salary/socialSecurity/viewEdit",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
  // 未打卡扣款
  noPunchDeduction: {
    //未打卡扣款列表
    getNoPunchDeduction(data) {
      return axios.post("/was-customized/unClock/list", data);
    },
    //查询考勤中的班组信息
    getListGroups(data) {
      return axios.post(
        "/was-customized/unClock/listGroupsByCommonQuery",
        data
      );
    },
    //新增未打卡扣款
    addUnClock(data) {
      return axios.post("/was-customized/unClock/addUnClock", data);
    },
    //编辑未打卡扣款
    editNoPunchDeduction(data) {
      return axios.post("/was-customized/unClock/editUnClock", data);
    },
    //删除未打卡扣款
    deleteUnClock(data) {
      return axios({
        url: "/was-customized/unClock/deleteUnClock",
        method: "get",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //未打卡扣款统计
    statisticsNoPunchDeduction(data) {
      return axios.post("/was-customized/unClock/statistics", data);
    },
  },
  // 工会费
  unionFee: {
    //工会费列表
    getUnionFee(data) {
      return axios.post("/was-customized/unionFees/list", data);
    },
    //编辑工会费
    editUnionFee(data) {
      return axios.post("/was-customized/unionFees/editUnionFees", data);
    },
    //工会费统计
    statistictUnionFee(data) {
      return axios.post("/was-customized/unionFees/statistics", data);
    },
    //工会费详情
    detailUnionFee(data) {
      return axios({
        url: "/was-customized/unionFees/detail",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //批量开启或关闭工会费扣款
    batchOnOrOffUnionFees(data) {
      return axios.post(
        "/was-customized/unionFees/batchOnOrOffUnionFees",
        data
      );
    },
  },
  //厂服扣款
  factoryUniform: {
    //厂服扣款列表
    getFactoryUniform(data) {
      return axios.post("/was-customized/factoryUniform/list", data);
    },
    //厂服扣款统计
    factoryUniformStatistics(data) {
      return axios.post("/was-customized/factoryUniform/statistics", data);
    },
  },
  //公司补贴
  coSubsidy: {
    //公司补贴列表
    getCoSubsidy(data) {
      return axios.post("/was-customized/coAllowance/list", data);
    },
    //公司补贴统计
    coSubsidyStatistic(data) {
      return axios.post("/was-customized/coAllowance/statistics", data);
    },
  },
  //厂牌扣款
  factoryCard: {
    //厂牌扣款列表
    getFactoryCard(data) {
      return axios.post("/was-customized/factoryCard/list", data);
    },
    //厂牌扣款统计
    factoryCardStatistic(data) {
      return axios.post("/was-customized/factoryCard/statistics", data);
    },
  },
  //住房补贴
  rentalAllowance: {
    //住房补贴列表
    getRentalAllowance(data) {
      return axios.post("/was-customized/rentalAllowance/list", data);
    },
    //住房补贴统计
    rentalAllowanceStatistic(data) {
      return axios.post("/was-customized/rentalAllowance/statistics", data);
    },
  },
  //生活费
  livingCosts: {
    //生活费列表
    getLivingCosts(data) {
      return axios.post("/was-customized/livingCosts/list", data);
    },
    //生活费统计
    livingCostsStatistic(data) {
      return axios.post("/was-customized/livingCosts/statistics", data);
    },
  },
  //环境补贴
  environmentSubsidy: {
    //环境补贴新增
    environmentSubsidy(data) {
      return axios.post("/was-customized/environmentSubsidy/add", data);
    },
    //环境补贴删除
    environmentSubsidydelete(data) {
      return axios.post("/was-customized/environmentSubsidy/delete", data);
    },
    //环境补贴编辑
    environmentSubsidyedit(data) {
      return axios.post("/was-customized/environmentSubsidy/edit", data);
    },
    //环境补贴编辑
    environmentSubsidylist(data) {
      return axios.post("/was-customized/environmentSubsidy/list", data);
    },
    //根据计件工序获取员工的计件班组
    listGroups(data) {
      return axios.post("/was-customized/processFromPiece/listGroups", data);
    },
    //环境补贴统计
    statistics(data) {
      return axios.post("/was-customized/environmentSubsidy/statistics", data);
    },
    //根据考勤生成环境补贴
    generate(data) {
      return axios.post("/was-customized/environmentSubsidy/generate", data);
    },
  },
  //新员工补贴20%扣款
  newemployee: {
    //新员工补贴20%列表
    newemployeeList(data) {
      return axios.post("/was-customized/newStaff/deduction/list", data);
    },
    //新员工补贴20%新增
    newemployeeAdd(data) {
      return axios.post("/was-customized/newStaff/deduction/add", data);
    },
    //新员工补贴20%编辑
    newemployeeEdit(data) {
      return axios.post("/was-customized/newStaff/deduction/edit", data);
    },
    //新员工补贴20%删除
    newemployeeDelete(data) {
      return axios.post("/was-customized/newStaff/deduction/delete", data);
    },
    //新员工补贴20%统计
    newemployeestatistics(data) {
      return axios.post("/was-customized/newStaff/deduction/statistics", data);
    },
    //新员工补贴20%生成环境补贴
    newemployeeGenerate(data) {
      return axios.post("/was-customized/newStaff/deduction/generate", data);
    },
  },
  //熟手补贴
  ldhandsubsidy: {
    //熟手补贴列表
    getList(data) {
      return axios.post("/was-customized/skilledSubsidy/list", data);
    },
    //熟手补贴统计
    subsidystatistics(data) {
      return axios.post("/was-customized/skilledSubsidy/statistics", data);
    },
    //熟手补贴新增
    subsidyadd(data) {
      return axios.post("/was-customized/skilledSubsidy/add", data);
    },
    //熟手补贴修改
    subsidyedit(data) {
      return axios.post("/was-customized/skilledSubsidy/edit", data);
    },
    //熟手补贴删除
    subsidydelete(data) {
      return axios.post("/was-customized/skilledSubsidy/delete", data);
    },
    //生成数据
    subsidygenerate(data) {
      return axios.post("/was-customized/skilledSubsidy/generate", data);
    },
  },
  //新员工补贴20%扣款
  newemployee: {
    //新员工补贴20%列表
    newemployeeList(data) {
      return axios.post("/was-customized/newStaff/deduction/list", data);
    },
    //新员工补贴20%新增
    newemployeeAdd(data) {
      return axios.post("/was-customized/newStaff/deduction/add", data);
    },
    //新员工补贴20%编辑
    newemployeeEdit(data) {
      return axios.post("/was-customized/newStaff/deduction/edit", data);
    },
    //新员工补贴20%删除
    newemployeeDelete(data) {
      return axios.post("/was-customized/newStaff/deduction/delete", data);
    },
    //新员工补贴20%统计
    newemployeestatistics(data) {
      return axios.post("/was-customized/newStaff/deduction/statistics", data);
    },
    //新员工补贴20%生成环境补贴
    newemployeeGenerate(data) {
      return axios.post("/was-customized/newStaff/deduction/generate", data);
    },
  },
  //低耗品
  lowConsumption: {
    //低耗品列表
    getLowConsumption(data) {
      return axios.post("/was-customized/lowConsumption/list", data);
    },
    //低耗品统计
    lowConsumptionStatistic(data) {
      return axios.post("/was-customized/lowConsumption/statistics", data);
    },
  },
  //体检费
  physicalExam: {
    //体检费列表
    getPhysicalExam(data) {
      return axios.post("/was-customized/physicalExam/list", data);
    },
    //体检费统计
    physicalExamStatistic(data) {
      return axios.post("/was-customized/physicalExam/statistics", data);
    },
    //体检费明细
    physicalDetail(data) {
      return axios.post("/was-customized/physicalExam/viewDetail", data);
    },
    //体检费编辑
    editSave(data) {
      return axios.post("/was-customized/physicalExam/editSave", data);
    },
  },
  //成本赔偿
  costCompensation: {
    //成本赔偿列表
    getCostCompensation(data) {
      return axios.post("/was-customized/bpmDeductList/list", data);
    },
    //成本赔偿统计
    costCompensationStatistic(data) {
      return axios.post("/was-customized/bpmDeductList/statistic", data);
    },
  },
  //返工扣款
  reworkCut: {
    //返工扣款列表
    getReworkCut(data) {
      return axios.post("/was-customized/reworkCut/list", data);
    },
    //返工扣款统计
    reworkCutStatistic(data) {
      return axios.post("/was-customized/reworkCut/statistics", data);
    },
    //返工扣款新增
    getReadd(data) {
      return axios.post("/was-customized/reworkCut/add", data);
    },
    //返工扣款编辑
    getReedit(data) {
      return axios.post("/was-customized/reworkCut/edit", data);
    },
    //返工扣款删除
    getRedelete(data) {
      return axios.post("/was-customized/reworkCut/delete", data);
    },
    //返工扣款刷新
    getRerefresh(data) {
      return axios.post("/was-customized/reworkCut/refresh", data);
    },
    //返工扣款导入
    getRereimport(data) {
      return axios.post("/was-customized/reworkCut/import", data);
    },
    //刷新效验数据是否有变动
    checkRefresh(data) {
      return axios.post("/was-customized/reworkCut/checkRefresh", data);
    },
  },
  //杂工考勤
  backManAttendance: {
    //杂工考勤列表
    getBackManAttendanceList(data) {
      return axios.post("/was-customized/backManAttendance/list", data);
    },
    //杂工考勤统计
    backManAttendanceStatistic(data) {
      return axios.post("/was-customized/backManAttendance/statistic", data);
    },
    //杂工考勤新增
    addBackManAttendance(data) {
      return axios.post("/was-customized/backManAttendance/add", data);
    },
    //杂工考勤编辑
    editBackManAttendance(data) {
      return axios.post("/was-customized/backManAttendance/edit", data);
    },
    //杂工考勤详情
    backManAttendanceView(data) {
      return axios({
        url: "/was-customized/backManAttendance/view",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //删除
    deleteBackManAttendance(data) {
      return axios({
        url: "/was-customized/backManAttendance/delete",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
};
// 数据配置
export const dataConfiguration = {
  dataConfigList(data) {
    return axios.post("/was-customized/data/config/list", data);
  },
  dataConfigSave(data) {
    return axios.post("/was-customized/data/config/save", data);
  },
  dataConfigUpdate(data) {
    return axios.post("/was-customized/data/config/update", data);
  },
  dataConfigToggle(data) {
    return axios.get(`/was-customized/data/config/toggle?id=${data}`);
  },
};
// 系统配置
export const systemConfig = {
  //根据工厂查询工厂下工序
  listBasicConfigByParentId(data) {
    return axios({
      url: `/was-customized/system/basicConfig/listBasicConfigByParentId?factoryId=${data.factoryId}&enable=${data.enable}`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
  },

  // 未关联配置项查询
  listUnassignedItem(data) {
    return axios.post("/was-customized/data/config/listUnassignedItem", data);
  },
  //任务配置分页查询
  taskConfigPage({ pageNum, pageSize, filterData }) {
    return axios.post("/was-customized/system/other/plan/list", {
      pageNum,
      pageSize,
      filterData,
    });
  },
  //任务配置新增
  taskConfigAdd(data) {
    return axios.post("/was-customized/system/other/plan/save", data);
  },
  // 启用禁用
  taskConfigtToggle(data) {
    return axios.get(`/was-customized/system/other/plan/toggle?id=${data}`);
  },
  //任务配置编辑
  taskConfigEdit(data) {
    return axios.post("/was-customized/system/other/plan/update", data);
  },
  //任务配置删除
  taskConfigDel(data) {
    return axios({
      url: "/was-customized/system/other/plan/delete",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //任务配置模板下载
  taskConfigDownload(data) {
    return axios({
      url: "/was-customized/system/other/plan/template/download",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      responseType: "blob",
      params: data,
    });
  },
  //页面配置查询
  pageConfig() {
    return axios.post("/was-customized/system/other/page/list");
  },
  //页面配置空数据隐藏或显示
  pageConfigEmptyData(data) {
    return axios.post("/was-customized/system/other/page/showOrHide", data);
  },
  //页面配置拖拽排序
  pageConfigdDrag(data) {
    return axios.post("/was-customized/system/other/page/drag", data);
  },
  //动态列头
  dynamicheader(params) {
    return axios.get("/was-customized/system/other/head", {
      params,
    });
  },
};
//系统管理
export const systemManage = {
  //基础信息
  getBasicPermission: {
    //获取带数据权限工厂
    getBasicPermissionAll(data) {
      return axios.post("/was-customized/system/basicConfig/getDetail", data);
    },

    //获取所有工厂
    getBasicPermissionAll1() {
      return axios({
        url: "/was-customized/system/basicConfig/listAllFactories",
        method: "get",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    //可用的工厂列表
    availableFactories() {
      return axios.post(
        "/was-customized/system/basicConfig/availableFactories"
      );
    },
    //获取所有基础信息
    getAllFactory(data) {
      return axios.post(
        "/was-customized/system/basicConfig/listAllFactory",
        data
      );
    },
    belongFactories(data) {
      return axios.post(
        "/was-customized/salary/socialSecurity/belongFactories",
        data
      );
    },
    // 编辑班组信息
    editGroup(data) {
      return axios.post("/was-customized/system/basicConfig/editGroup", data);
    },
    // 新增班组信息
    addGroup(data) {
      return axios.post("/was-customized/system/basicConfig/saveGroup", data);
    },
    // 通过ID删除数据
    getBasicPermissionDeleteById(id) {
      return axios({
        url: "/was-customized/system/basicConfig/deleteById",
        id,
      });
    },
    //启用  禁用
    enable(data) {
      return axios({
        url: "/was-customized/system/basicConfig/enable",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    // availableGroup(data) {
    //   return axios({
    //     url: "/was-customized/system/basicConfig/listByFactoryId",
    //     method: "post",
    //     headers: {
    //       "Content-Type": "application/x-www-form-urlencoded",
    //     },
    //     params: data,
    //   });
    // },
    //根据工厂和核算月份查询班组列表
    listByFactoryId(data) {
      return axios({
        url: "/was-customized/system/basicConfig/availableGroups",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
    //环境补贴参数配置列表
    getConfigList(data) {
      return axios.post(`/was-customized/parameter/config/list`, data);
    },
    //环境补贴参数配置新增/修改
    saveOrUpdate(data) {
      return axios.post("/was-customized/parameter/config/saveOrUpdate", data);
    },
    //环境补贴参数配置删除/批量删除
    removedelete(data) {
      return axios.post("/was-customized/parameter/config/remove", data);
    },
    //环境补贴参数配置修改
    updateParamConfigGlobal(data) {
      return axios.post(
        "/was-customized/parameter/config/updateParamConfigGlobal",
        data
      );
    },
    //环境补贴参数配置查询
    getParamConfigGlobal(taskType) {
      return axios.get(
        `/was-customized/parameter/config/getParamConfigGlobal?taskType=${taskType}`
      );
    },
    //参数配置修改
    configUpdate(data) {
      return axios.post("/was-customized/parameter/config/update", data);
    },
    //计件工序
    listProcessFromPiece(factoryId) {
      return axios.get(
        `/was-customized/processFromPiece/listProcessFromPiece?factoryId=${factoryId}`
      );
    },
    //熟手补贴列表
    configList(data) {
      return axios.post("/was-customized/newStaff/config/rule/list", data);
    },
    //熟手补贴列表新增
    configAdd(data) {
      return axios.post("/was-customized/newStaff/config/rule/add", data);
    },
    //熟手补贴列表修改
    configedit(data) {
      return axios.post("/was-customized/newStaff/config/rule/edit", data);
    },
    //熟手补贴列表删除
    configdelete(id) {
      return axios.get(`/was-customized/newStaff/config/rule/delete?id=${id}`);
    },
    //熟手补贴列表批量删除
    configbatchDelete(ids) {
      return axios.get(
        `/was-customized/newStaff/config/rule/batchDelete?ids=${ids}`
      );
    },
    //熟手参数全局配置
    getConfigGlobal(params) {
      return axios.post(
        `/was-customized/newStaff/config/rule/getConfigGlobal`,
        params,
        { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
      );
    },
    //熟手补贴参数配置编辑保存
    updateConfigGlobal(data) {
      return axios.post(
        "/was-customized/newStaff/config/rule/updateConfigGlobal",
        data
      );
    },
    //新员工补贴20%参数配置查询
    employeeConfigGlobal(taskType) {
      return axios.get(
        `/was-customized/newStaff/deduction/config/list?taskType=${taskType}`
      );
    },
    //新员工补贴20%参数配置编辑保存
    editConfigGlobal(data) {
      return axios.post(
        "/was-customized/newStaff/deduction/config/update",
        data
      );
    },
  },
  // 数据权限
  DataPermission: {
    // 获取用户数据权限
    getUserPermission({ pageNum, pageSize }) {
      return axios({
        url: "/was-customized/salary/userPermission/getUserList",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: { pageNum, pageSize },
      });
    },
  },
  // 系统日志
  SystemLog: {
    //分页获取日志列表
    getBasicPermissionSaveOrUpdate(data) {
      return axios.post("/was-customized/salary/log/list"), { data };
    },
  },
  //大工序管理
  largeprocess: {
    //大工序列表
    listBigGroup(data) {
      return axios.post(
        "/was-customized/system/basicConfig/listBigGroup",
        data
      );
    },
    //大工序删除
    deleteBigGroup(data) {
      return axios({
        url: "/was-customized/system/basicConfig/deleteBigGroup",
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        params: data,
      });
    },
  },
};
//导入列表
export const importList = {
  //获取Excel配置列表
  getExcelList({ pageNum, pageSize, filterData }) {
    return axios.post("/qu-platform-excel-api/excel/cfg/list", {
      pageNum,
      pageSize,
      filterData,
    });
  },
  //获取模板
  getTemplate({ reportName }) {
    return axios({
      url: `/qu-platform-excel-api/excel/cfg/getTemplate/${reportName}`,
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
  },
  //自定义列导入
  getCustomColumnImportant(data) {
    return axios.post(
      "/qu-platform-excel-api/excel/customColumn/import/task",
      data
    );
  },
};
export const roleInfo = {
  // 获取所有角色信息
  getRoleInfoAll(data) {
    return axios({
      url: "/was-customized/salary/role/page/list",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //获取角色列表
  getRoleList(data) {
    return axios({
      url: "/was-customized/salary/role/userRoles",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
};
export const agencyTasks = {
  //获取待办任务列表
  getBacklogList(data) {
    return axios.post("/was-customized/salary/backlog/list", data);
  },
};
export const agencyLog = {
  //获取待办任务列表
  logList(data) {
    return axios.post("/was-customized/salary/log/list", data);
  },
  userPermissionAgencyTasks(data) {
    return axios.post(
      "/was-customized/salary/userPermission/getUserList",
      data
    );
  },
  editPermission(data) {
    return axios.post(
      "/was-customized/salary/userPermission/editPermission",
      data
    );
  },
  getEditList(data) {
    return axios.post(
      "/was-customized/salary/factoryEditSalary/getEditList",
      data
    );
  },
  submitClick(params) {
    return axios({
      url: "/was-customized/salary/factoryEditSalary/submit",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params,
    });
  },
  importClick(data) {
    return axios.post("/was-customized/salary/factoryEditSalary/import", data);
  },
  socialSecurity(data) {
    return axios.post("/was-customized/salary/socialSecurity/list", data);
  },
  editPermission(data) {
    return axios.post(
      "/was-customized/salary/userPermission/editPermission",
      data
    );
  },
  syncAdd(data) {
    return axios.post("/was-customized/salary/socialSecurity/syncAdd", data);
  },
};
export const exportApI = {
  //获取导入模块列表
  getImportModules() {
    return axios({
      url: "/was-customized/salary/work/listImportModules",
      method: "get",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
  },
  //获取导入任务
  getImportTask(data) {
    return axios.post("/was-customized/salary/work/listImportTask", data);
  },
  otherHead(planId, type, factoryId, accountingMonth) {
    return axios.get(
      `/was-customized/system/other/head?planId=${planId}&type=${type}&factoryId=${factoryId}&accountingMonth=${accountingMonth}`
    );
  },
};
// 统计
export const statiStics = {
  statisticsSalary(data) {
    return axios.post("/was-customized/salary/salary/statistics", data);
  },
  statisticsBankCash(data) {
    return axios.post("/was-customized/salary/bankCash/statistics", data);
  },
  statisticsAllot(data) {
    return axios.post("/was-customized/salary/allot/statistics", data);
  },
  // 计件工资操作权限
  getPieceWagePermission(data) {
    return axios.post(
      "/was-customized/salary/work/getPieceWagePermission",
      data
    );
  },
  // 其他扣款统计
  statisticsOtherDeduction(data) {
    return axios.post("/was-customized/otherDeduction/statistics", data);
  },
  // 其他补贴统计
  statisticsOtherSubsidy(data) {
    return axios.post("/was-customized/otherSubsidy/statistics", data);
  },
  statisticsOthegetCompleteTaskPermissionrSubsidy(data) {
    return axios.post(
      "/was-customized/salary/work/getCompleteTaskPermission",
      data
    );
  },
};
//报表管理
export const reportManagement = {
  //财务汇总表
  financialSummary(data) {
    return axios({
      url: "/was-customized/summaryFinancial/list",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //导出
  exportFinancialSummary(data) {
    return axios({
      url: "/was-customized/summaryFinancial/export",
      method: "post",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      params: data,
    });
  },
  //工资分析表
  salaryAnalysis(data) {
    return axios.post("/was-customized/salaryAnalysis/list", data);
  },
  //工资分析表导出
  exportSalaryAnalysis(data) {
    return axios.post("/was-customized/salaryAnalysis/export", data);
  },
  //返工扣款明细
  reworkFromlist(data) {
    return axios.post("/was-customized/reworkFrom/list", data);
  },
  //工资划拨分析表明细表
  wageTransferAnalyzeDetailed(data) {
    return axios.post("/was-customized/salary/transfer/report/listDetail", data);
  },
  //工资划拨分析表汇总表
  wageTransferAnalyzeSummary(data) {
    return axios.post("/was-customized/salary/transfer/report/listSummary", data);
  },
  //工资划拨分析表导出
  exportWageTransferAnalyze(data) {
    return axios.post("/was-customized/salary/transfer/report/export", data);
  },
  //人工效率工序管理列表
  getLaborEfficiencyList(data) {
    return axios.post("/was-customized/labor/efficiency/group/list", data);
  },
  //人工效率工序查询
  getLaborEfficiencyLaborEffect(data) {
    return axios.post("/was-customized/labor/efficiency/group/getNameByFactoryId", data);
  },
  //人工效率工序新增
  addLaborEfficiency(data) {
    return axios.post("/was-customized/labor/efficiency/group/addSave", data);
  },
  //人工效率工序删除
  delLaborEfficiency(id) {
    return axios.post("/was-customized/labor/efficiency/group/deleteGroup?id=" + id);
  },
  //人工效率工序编辑
  updateLaborEfficiency(data) {
    return axios.post("/was-customized/labor/efficiency/group/editSave", data);
  },
  //人工效率工序启用禁用
  enableLaborEfficiency(data) {
    return axios.post("/was-customized/labor/efficiency/group/enableOrDisable?id=" + data.id + '&enable=' + data.enable);
  },
  laborEfficiency:{
    //人工效率分析表
    getlaborEfficiencyList(data) {
      return axios.post("/was-customized/labor/efficiency/report/list", data);
    },
      //人工效率分析表导出
    exportLaborEfficiencyList(data) {
      return axios.post("/was-customized/labor/efficiency/report/export", data);
    },
  }
 
};
