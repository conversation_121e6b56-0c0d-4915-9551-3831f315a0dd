import Layout from "@/layout";
const plateTypeRouter = [
  {
    path: "/plateType/workbench",
    component: Layout,
    redirect: "/plateType/workbench/overview",
    meta: {
      title: "工作台",
    },
    children: [
      {
        name: "PlateTypeTask",
        path: "overview",
        component: () =>
          import("@/views-plateType/overview/agencyTask/agencyTask"),
        meta: {
          title: "待办任务",
        },
      },
      {
        name: "PlateTypeTaskDetails",
        path: "taskDetails",
        component: () => import("@/views-plateType/overview/taskDetails"),
        meta: {
          title: "任务明细",
        },
      },
      {
        name: "plateTypePayroll",
        path: "payroll",
        component: () => import("@/views-plateType/overview/payroll/payroll"),
        meta: {
          title: "工资表",
        },
      },
      {
        name: "PlateTypeAdjustment",
        path: "adjustment",
        component: () =>
          import("@/views-plateType/overview/adjustment/adjustment"),
        meta: {
          title: "分厂调整",
        },
      },
      {
        name: "PlateTypeUpload",
        path: "upload",
        component: () => import("@/views-plateType/overview/upload/upload"),
        meta: {
          title: "个税扣款",
        },
      },
    ],
  },
  {
    path: "/plateType/account",
    component: Layout,
    redirect: "/plateType/account/debitLedger",
    meta: {
      title: "信息台账",
    },
    children: [
      {
        name: "PlateTypeRemaining",
        path: "remaining",
        component: () =>
          import("@/views-plateType/account/remaining/ocale/ocale"),
        meta: {
          title: "余留管理",
        },
      },
      {
        name: "PlateTypeRemainingDetailed",
        path: "remainingDetailed",
        component: () =>
          import("@/views-plateType/account/remaining/remaining"),
        meta: {
          title: "余留调整明细",
        },
      },
       {
        name: "PlateTypeDebitLedger",
        path: "debitLedger",
        component: () =>
          import("@/views-plateType/account/debitLedger/debitLedger"),
        meta: {
          title: "借支台账",
        },
      },
      {
        name: "PlateTypeOrderBomRevision",
        path: "orderBomRevision",
        component: () =>
          import("@/views-plateType/account/orderBomRevision/orderBomRevision"),
        meta: {
          title: "订单BOM修订",
        },
      },
    {
        name: "PlateTypeFactoryAdjustment",
        path: "factoryAdjustment",
        component: () =>
          import("@/views-plateType/account/factoryAdjustment/factoryAdjustment"),
        meta: {
          title: "分厂调整",
        },
      },

      {
        name: "PlateTypeDebitList",
        path: "debitList",
        component: () => import("@/views-plateType/account/debitList"),
        meta: {
          title: "借支清单",
        },
      },
      {
        name: "PlateTypeReward",
        path: "reward",
        component: () => import("@/views-plateType/account/reward/reward"),
        meta: {
          title: "奖惩台账",
        },
      },
      {
        name: "PlateTypeRewardList",
        path: "rewardList",
        component: () => import("@/views-plateType/account/rewardList"),
        meta: {
          title: "奖惩清单",
        },
      },
      {
        name: "PlateTypePayroll",
        path: "specialPayrollMgent",
        component: () =>
          import(
            "@/views-plateType/account/payrollManagement/payrollManagement"
          ),
        meta: {
          title: "特殊工资单管理",
        },
      },
      {
        name: "PlateTypeSpecialPayroll",
        path: "specialPayroll",
        component: () => import("@/views-plateType/account/specialPayroll"),
        meta: {
          title: "特殊工资单查看",
        },
      },
      {
        name: "PlateTypeCompensation",
        path: "compensation",
        component: () =>
          import("@/views-plateType/account/costCompensation/costCompensation"),
        meta: {
          title: "成本赔偿扣款台账",
        },
      },
      {
        name: "PlateTypeUnpaidLedger",
        path: "unpaidLedger",
        component: () =>
          import("@/views-plateType/account/unpaidLedger/unpaidLedger"),
        meta: {
          title: "未付款台账",
        },
      },
      {
        name: "PlateTypeSalarySearch",
        path: "salarySearch",
        component: () => import("@/views-plateType/account/salarySearch"),
        meta: {
          title: "工资查询",
        },
      },
      //新员工补贴20%扣款
      {
        name: "PlateTypeNewemployeeSubsidy",
        path: "newemployeeSubsidy",
        component: () =>
          import(
            "@/views-plateType/account/newemployeeSubsidy/newemployeeSubsidy"
          ),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
      //新/熟手补贴名单管理
      {
        name: "PlateTypeNewEmployeeSubsidies",
        path: "newEmployeeSubsidies",
        component: () =>
          import(
            "@/views-plateType/account/newEmployeeSubsidies/newEmployeeSubsidies"
          ),
        meta: {
          title: "新/熟手补贴名单管理",
        },
      },
    ],
  },
  {
    path: "/plateType/uploadTasks",
    component: Layout,
    redirect: "/plateType/uploadTasks/attendance",
    meta: {
      title: "数据上传",
    },
    children: [
      {
        name: "PlateTypeAttendance",
        path: "attendance",
        component: () =>
          import("@/views-plateType/uploadTasks/attendance/attendance"),
        meta: {
          title: "员工考勤",
        },
      },
      {
        name: "PlateTypeWage",
        path: "wage",
        component: () => import("@/views-plateType/uploadTasks/wage/wage"),
        meta: {
          title: "计件工资",
        },
      },
      {
        name: "PlateTypeCollective",
        path: "collective",
        component: () => import("@/views-plateType/uploadTasks/collective"),
        meta: {
          title: "集体账户",
        },
      },
      {
        name: "PlateTypeSubsidy",
        path: "subsidy",
        component: () =>
          import("@/views-plateType/uploadTasks/subsidy/subsidy"),
        meta: {
          title: "其他补贴",
        },
      },
      {
        name: "PlateTypeDeduction",
        path: "deduction",
        component: () =>
          import("@/views-plateType/uploadTasks/deduction/deduction"),
        meta: {
          title: "其他扣款",
        },
      },
      {
        name: "PlateTypeSecurity",
        path: "security",
        component: () =>
          import("@/views-plateType/uploadTasks/security/security"),
        meta: {
          title: "社保扣款",
        },
      },
      {
        name: "PlateTypePuncandeduct",
        path: "puncandeduct",
        component: () =>
          import("@/views-plateType/uploadTasks/puncandeduct/puncandeduct"),
        meta: {
          title: "未打卡扣款",
        },
      },
      {
        name: "PlateTypeUnionfee",
        path: "unionfee",
        component: () =>
          import("@/views-plateType/uploadTasks/unionfee/unionfee"),
        meta: {
          title: "工会费",
        },
      },
      {
        name: "PlateTypeCompanysubsidies",
        path: "companysubsidies",
        component: () =>
          import("@/views-plateType/uploadTasks/companysubsidies"),
        meta: {
          title: "公司补贴",
        },
      },
      {
        name: "PlateTypeHousingsubsidies",
        path: "housingsubsidies",
        component: () =>
          import("@/views-plateType/uploadTasks/housingsubsidies"),
        meta: {
          title: "住房补贴",
        },
      },
      {
        name: "PlateTypeFactoryservicededuction",
        path: "factoryservicededuction",
        component: () =>
          import("@/views-plateType/uploadTasks/factoryservicededuction"),
        meta: {
          title: "厂服扣款",
        },
      },
      {
        name: "PlateTypeFactorycarddeduction",
        path: "factorycarddeduction",
        component: () =>
          import("@/views-plateType/uploadTasks/factorycarddeduction"),
        meta: {
          title: "厂牌扣款",
        },
      },
      {
        name: "PlateTypeLifefei",
        path: "lifefei",
        component: () => import("@/views-plateType/uploadTasks/lifefei"),
        meta: {
          title: "生活费",
        },
      },
      {
        name: "PlateTypePhysicalexamination",
        path: "physicalexamination",
        component: () =>
          import(
            "@/views-plateType/uploadTasks/physicalexamination/physicalexamination"
          ),
        meta: {
          title: "体检费",
        },
      },
      {
        name: "PlateTypeCostcompensationList",
        path: "costcompensationList",
        component: () =>
          import("@/views-plateType/uploadTasks/costcompensationList"),
        meta: {
          title: "成本赔偿清单",
        },
      },
      {
        name: "PlateTypeCostcompensation",
        path: "costcompensation",
        component: () =>
          import("@/views-plateType/uploadTasks/costcompensation"),
        meta: {
          title: "成本赔偿",
        },
      },
      {
        name: "PlateTypeLowconsumptiongoods",
        path: "lowconsumptiongoods",
        component: () =>
          import("@/views-plateType/uploadTasks/lowconsumptiongoods"),
        meta: {
          title: "低耗品",
        },
      },
      {
        name: "PlateTypeReworkdeduction",
        path: "reworkdeduction",
        component: () =>
          import("@/views-plateType/uploadTasks/reworkdeduction"),
        meta: {
          title: "返工扣款",
        },
      },
      {
        name: "PlateTypeMiscellaneous",
        path: "miscellaneous",
        component: () =>
          import("@/views-plateType/uploadTasks/miscellaneous/miscellaneous"),
        meta: {
          title: "杂工考勤",
        },
      },
      {
        name: "PlateTypeenvironmental",
        path: "environmental",
        component: () =>
          import("@/views-plateType/uploadTasks/environmental/environmental"),
        meta: {
          title: "环境补贴",
        },
      },
      {
        name: "PlateTypemanagementsystem",
        path: "managementsystem",
        component: () =>
          import(
            "@/views-plateType/uploadTasks/managementsystem/managementsystem"
          ),
        meta: {
          title: "全勤奖",
        },
      },
      {
        name: "PlateTypehightemperature",
        path: "hightemperature",
        component: () =>
          import(
            "@/views-plateType/uploadTasks/hightemperature/hightemperature"
          ),
        meta: {
          title: "高温补贴",
        },
      },
      {
        name: "PlateTypeemployeesubsidies",
        path: "employeesubsidies",
        component: () =>
          import(
            "@/views-plateType/uploadTasks/employeesubsidies/employeesubsidies"
          ),
        meta: {
          title: "新员工补贴",
        },
      },
      {
        name: "PlateTypeoldhandsubsidy",
        path: "oldhandsubsidy",
        component: () =>
          import("@/views-plateType/uploadTasks/oldhandsubsidy/oldhandsubsidy"),
        meta: {
          title: "熟手补贴",
        },
      },
      {
        name: "PlateTypeOewemployeeSubsidy",
        path: "newemployeeSubsidy",
        component: () =>
          import(
            "@/views-plateType/uploadTasks/newemployeeSubsidy/newemployeeSubsidy"
          ),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
    ],
  },
  {
    path: "/plateType/system",
    component: Layout,
    redirect: "/plateType/system/dataconfiguration",
    meta: {
      title: "系统配置",
    },
    children: [
      {
        name: "plateTypePageConfig",
        path: "pageconfig",
        component: () => import("@/views-plateType/system-config/pageConfig"),
        meta: {
          title: "表头管理",
        },
      },
      {
        name: "PlateTypeTaskConfig",
        path: "taskconfig",
        component: () =>
          import("@/views-plateType/system-config/taskConfig/taskConfig"),
        meta: {
          title: "任务配置",
        },
      },
      {
        name: "PlateTypeConfiguration",
        path: "dataconfiguration",
        component: () =>
          import(
            "@/views-plateType/system-config/dataConfiguration/dataConfiguration"
          ),
        meta: {
          title: "数据配置",
        },
      },
      {
        name: "PlateTypeconfiguratask",
        path: "configuratask",
        component: () =>
          import("@/views-plateType/system-config/confiGuratask/confiGuratask"),
        meta: {
          title: "参数配置",
        },
      },
      //熟手补贴配置
      {
        name: "PlateTypeoldhand",
        path: "config-oldhand",
        component: () =>
          import(
            "@/views-plateType/system-config/config-oldhand/config-oldhand"
          ),
        meta: {
          title: "熟手补贴配置",
        },
      },
      //新手补贴配置
      {
        name: "PlateTypenovicesubsidy",
        path: "novice-subsidy",
        component: () =>
          import(
            "@/views-plateType/system-config/novice-subsidy/config-oldhand"
          ),
        meta: {
          title: "新员工补贴配置",
        },
      },
      //高温补贴配置
      {
        name: "PlateTypehightbsidy",
        path: "hight-bsidy",
        component: () =>
          import("@/views-plateType/system-config/hight-bsidy/config-oldhand"),
        meta: {
          title: "高温补贴配置",
        },
      },
      //全勤补贴配置
      {
        name: "PlateTypemanagementsystem",
        path: "manage-mentsystem",
        component: () =>
          import(
            "@/views-plateType/system-config/manage-mentsystem/config-oldhand"
          ),
        meta: {
          title: "全勤补贴配置",
        },
      },
      //环境补贴配置
      {
        name: "PlateTypeconfiguration",
        path: "config-uration",
        component: () =>
          import(
            "@/views-plateType/system-config/config-uration/config-uration"
          ),
        meta: {
          title: "环境补贴配置",
        },
      },
      //新员工补贴20%配置
      {
        name: "PlateTypeConfigNewemployee",
        path: "config-newemployee",
        component: () =>
          import("@/views-plateType/system-config/config-newemployee/newemployee"),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
      {
        name: "PlateTypeAuxiliaryWorker",
        path: "auxiliaryWorker",
        component: () =>
          import("@/views-plateType/system-config/config-auxiliaryWorker/auxiliaryWorker"),
        meta: {
          title: "辅助工配置",
        },
      },
    ],
  },
  {
    path: "/plateType/pieceworkWage",
    component: Layout,
    redirect: "/plateType/pieceworkWage/pieceworkWageDetails",
    meta: {
      title: "工资管理",
    },
    children: [
      {
        name: "PlateTypePieceworkWageDetails",
        path: "pieceworkWageDetails",
        component: () => import("@/views-plateType/wage-management/pieceworkWageDetails/pieceworkWageDetails"),
        meta: {
          title: "计件工资查询",
        },
      },
      {
        name: "PlateTypeDailyPieceworkQuery",
        path: "dailyPieceworkQuery",
        component: () => import("@/views-plateType/wage-management/dailyPieceworkQuery/dailyPieceworkQuery"),
        meta: {
          title: "日均计件统计表",
        },
      },
      {
        name: "PlateTypeWeekTodoTasks",
        path: "weekTodoTasks",
        component: () => import("@/views-plateType/wage-management/weekTodoTasks/weekTodoTasks"),
        meta: {
          title: "周待办任务",
        },
      },
      {
        name: "PlateTypeWeeklyPayroll",
        path: "weeklyPayroll",
        component: () => import("@/views-plateType/wage-management/weekTodoTasks/weeklyPayroll/weeklyPayroll"),
        meta: {
          title: "周工资表",
        },
      },
      {
        name: "PlateTypeWeeklyTaskList",
        path: "weeklyTaskList",
        component: () => import("@/views-plateType/wage-management/weekTodoTasks/weeklyTaskList/weeklyTaskList"),
        meta: {
          title: "周任务明细",
        },
      },
      {
        name: "PlateTypeWeeklyAttendance",
        path: "weeklyAttendance",
        component: () => import("@/views-plateType/wage-management/weekTodoTasks/weeklyAttendance/weeklyAttendance"),
        meta: {
          title: "员工考勤",
        },
      },
    ],
  },
  {
    path: "/plateType/manage",
    component: Layout,
    redirect: "/plateType/manage/employee",
    meta: {
      title: "系统管理",
    },
    children: [
      {
        name: "PlateTypeFactoryManagement",
        path: "factoryManagement",
        component: () =>
          import(
            "@/views-plateType/system-manage/factory-management/factory-management"
          ),
        meta: {
          title: "工厂管理",
        },
      },
      {
        name: "plateTypeEmployee",
        path: "employee",
        component: () =>
          import("@/views-plateType/system-manage/employee/employee"),
        meta: {
          title: "员工管理",
        },
      },
      {
        name: "plateTypeLargeprocess",
        path: "largeproces",
        component: () => import("@/views-plateType/system-manage/largeproces/largeproces"),
        meta: {
          title: "大工序管理",
        },
      },
      {
        name: "PlateTypeBasicInformation",
        path: "basic",
        component: () =>
          import(
            "@/views-plateType/system-manage/basic-information/basic-information"
          ),
        meta: {
          title: "班组管理",
        },
      },
      {
        name: "PlateTypeDataPermission",
        path: "data",
        component: () =>
          import(
            "@/views-plateType/system-manage/data-permission/data-permission"
          ),
        meta: {
          title: "数据权限",
        },
      },
      {
        name: "PlateTypeSystemLog",
        path: "systemlog",
        component: () => import("@/views-plateType/system-manage/system-log"),
        meta: {
          title: "系统日志",
        },
      },
    ],
  },
  {
    path: "/plateType/reportManager",
    component: Layout,
    redirect: "/plateType/reportManager/summary",
    children: [
      {
        name: "PlateTypeSalaryDifferenceWarningForm",
        path: "salaryDifferenceWarningForm",
        component: () => import("@/views-plateType/reportManager/salaryDifferenceWarningForm/salaryDifferenceWarningForm"),
        meta: {
          title: "工资差异预警表",
        },
      },
      {
        name: "plateTypeSummary",
        path: "summary",
        component: () => import("@/views-plateType/reportManager/summary"),
        meta: {
          title: "财务汇总表",
        },
      },
      {
        name: "PlateTypeWageTransferAnalyze",
        path: "wageTransfer-Analyze",
        component: () => import("@/views-plateType/reportManager/WageTransferAnalyze"),
        meta: {
          title: "工资划拨分析表",
        },
      },
      {
        name: "plateTypeSalaryAnalysis",
        path: "salaryAnalysis",
        component: () =>
          import("@/views-plateType/reportManager/salaryAnalysis"),
        meta: {
          title: "工资分析表",
        },
      },
      {
        name: "plateTypelaborEfficiency",
        path: "laborEfficiency",
        component: () =>
          import("@/views-plateType/reportManager/laborEfficiency"),
        meta: {
          title: "人工效率分析表",
        },
      },
      {
        name: "plateTypeAuxiliaryProductiom",
        path: "auxiliaryProductiom",
        component: () =>
          import("@/views-plateType/reportManager/auxiliaryProductiom/auxiliaryProductiom"),
        meta: {
          title: "辅助工产量管理",
        },
      },
    ],
  },
  {
    path: "/plateType/file",
    component: Layout,
    redirect: "/plateType/file/import",
    children: [
      {
        name: "PlateTypeImportFile",
        path: "import",
        component: () => import("@/views-plateType/import-export/import-list"),
        meta: {
          title: "导入列表",
        },
      },
      {
        name: "PlateTypeExportFile",
        path: "export",
        component: () => import("@/views-plateType/import-export/export-list"),
        meta: {
          title: "导出列表",
        },
      },
    ],
  },
];
export default plateTypeRouter;
